/**
 * Ordinance Modal JavaScript
 * Handles the Add Ordinance modal functionality
 */

// Ordinance Modal Functions
window.openAddOrdinanceModal = function() {
    openModal('addOrdinanceModal', 'ordinanceModalContent');
};

window.closeAddOrdinanceModal = function() {
    closeModal('addOrdinanceModal', 'ordinanceModalContent', 'addOrdinanceForm');
};

// Form validation
function validateOrdinanceForm() {
    const ordinanceNumber = document.getElementById('ordinance_number').value.trim();
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    const yearPassed = document.getElementById('year_passed').value;

    if (!ordinanceNumber || !title || !content || !yearPassed) {
        showNotification('Please fill in all required fields.', 'error');
        return false;
    }

    return true;
}

// Custom form submission for ordinance (with validation)
function setupOrdinanceFormSubmission() {
    const form = document.getElementById('addOrdinanceForm');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form first
        if (!validateOrdinanceForm()) {
            return;
        }

        const submitBtn = document.getElementById('ordinanceSubmitBtn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        // Submit form via AJAX
        const formData = new FormData(form);

        fetch('{% url "ordinances:admin_ordinance_create_ajax" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ordinance created successfully!', 'success');
                closeAddOrdinanceModal();
                // Refresh the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error creating ordinance.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
}

// Initialize ordinance modal functionality
function initializeOrdinanceModal() {
    // Setup click outside to close
    setupClickOutsideClose('addOrdinanceModal', closeAddOrdinanceModal);
    
    // Setup custom form submission with validation
    setupOrdinanceFormSubmission();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeOrdinanceModal();
});
