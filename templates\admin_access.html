{% extends 'base.html' %}
{% load static %}

{% block title %}Admin Access - Municipality of Dumingag{% endblock %}

{% block extra_css %}
<style>
/* Secure Admin Access Page Styling */
.admin-access-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #123458 0%, #1e3a5f 50%, #2a4a6b 100%);
    position: relative;
    overflow: hidden;
}

.admin-access-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("{% static 'img/dumingag-logo.png' %}") center/200px no-repeat;
    opacity: 0.05;
    pointer-events: none;
}

.security-badge {
    background: linear-gradient(45deg, #dc2626, #ef4444);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.admin-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

.admin-login-btn {
    background: linear-gradient(135deg, #123458 0%, #2563eb 100%);
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.admin-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
    color: white;
    text-decoration: none;
}

.warning-box {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid #f59e0b;
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
}

.back-to-public {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-to-public:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-access-container flex items-center justify-center p-4">
    <div class="admin-card max-w-md w-full p-8 text-center relative z-10">
        <!-- Security Badge -->
        <div class="mb-6">
            <div class="security-badge mx-auto">
                <i class="fas fa-shield-alt mr-2"></i>
                RESTRICTED ACCESS
            </div>
        </div>

        <!-- Logo -->
        <div class="mb-6">
            <img src="{% static 'img/dumingag-logo.png' %}"
                 alt="Dumingag Logo"
                 class="w-20 h-20 mx-auto mb-4">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Admin Access Portal</h1>
            <p class="text-gray-600">Municipality of Dumingag</p>
        </div>

        <!-- Warning Notice -->
        <div class="warning-box">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-amber-600 mr-3 mt-1"></i>
                <div class="text-left">
                    <h3 class="font-semibold text-amber-800 mb-1">Authorized Personnel Only</h3>
                    <p class="text-sm text-amber-700">
                        This area is restricted to authorized municipal staff only.
                        Unauthorized access is prohibited and monitored.
                    </p>
                </div>
            </div>
        </div>

        <!-- Admin Login Button -->
        <div class="mb-6">
            <a href="{% url 'ordinances:admin_login' %}" class="admin-login-btn">
                <i class="fas fa-key mr-3"></i>
                Admin Login
            </a>
        </div>

        <!-- Security Features -->
        <div class="text-xs text-gray-500 space-y-1 mb-6">
            <div class="flex items-center justify-center">
                <i class="fas fa-lock mr-2"></i>
                Secure SSL Connection
            </div>
            <div class="flex items-center justify-center">
                <i class="fas fa-eye mr-2"></i>
                Access Logging Enabled
            </div>
        </div>

        <!-- Back to Public Site -->
        <a href="{% url 'ordinances:home' %}" class="back-to-public">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Public Site
        </a>
    </div>
</div>

<!-- Additional Security Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Log access attempt (you can enhance this with actual logging)
    console.log('Admin access page viewed at:', new Date().toISOString());

    // Optional: Add additional security measures
    // - IP logging
    // - Rate limiting
    // - CAPTCHA integration
});
</script>
{% endblock %}
