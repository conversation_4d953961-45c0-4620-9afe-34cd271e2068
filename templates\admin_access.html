<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access Portal - Municipality of Dumingag</title>

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'img/dumingag-logo.png' %}">

    <!-- Custom TailwindCSS Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': {
                            'dark': '#123458',
                            'beige': '#D4C9BE',
                            'offwhite': '#F1EFEC'
                        },
                        'accent': {
                            'blue': '#2563eb',
                            'deep': '#1e40af'
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'matrix': 'matrix 20s linear infinite',
                        'scan': 'scan 3s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(37, 99, 235, 0.5)' },
                            '100%': { boxShadow: '0 0 40px rgba(37, 99, 235, 0.8)' }
                        },
                        matrix: {
                            '0%': { transform: 'translateY(-100%)' },
                            '100%': { transform: 'translateY(100vh)' }
                        },
                        scan: {
                            '0%, 100%': { transform: 'translateX(-100%)' },
                            '50%': { transform: 'translateX(100vw)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Enhanced Security Styling */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .security-grid {
            background-image:
                linear-gradient(rgba(37, 99, 235, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(37, 99, 235, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .security-scan {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(37, 99, 235, 0.3),
                transparent);
            animation: scan 4s ease-in-out infinite;
        }

        .hologram-effect {
            position: relative;
            overflow: hidden;
        }

        .hologram-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(0, 255, 255, 0.2),
                transparent);
            animation: hologram 3s ease-in-out infinite;
        }

        @keyframes hologram {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .cyber-border {
            position: relative;
            border: 2px solid transparent;
            background: linear-gradient(45deg, #123458, #2563eb) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .digital-noise {
            position: relative;
        }

        .digital-noise::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
            background-size: 20px 20px;
            animation: noise 0.5s steps(8, end) infinite;
            pointer-events: none;
        }

        @keyframes noise {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }
    </style>
</head>

<body class="bg-gradient-to-br from-primary-dark via-slate-900 to-primary-dark min-h-screen overflow-hidden"
      x-data="{
          isLoading: true,
          securityLevel: 'HIGH',
          accessAttempts: 0,
          showMatrix: true
      }"
      x-init="setTimeout(() => isLoading = false, 2000)">

    <!-- Matrix Background Effect -->
    <div class="matrix-bg security-grid" x-show="showMatrix"></div>

    <!-- Three.js Canvas -->
    <canvas id="security-canvas" class="fixed inset-0 w-full h-full pointer-events-none z-10"></canvas>

    <!-- Loading Screen -->
    <div x-show="isLoading"
         x-transition:leave="transition ease-in duration-500"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-accent-blue mx-auto mb-4"></div>
            <div class="text-accent-blue font-mono text-lg">INITIALIZING SECURITY PROTOCOLS...</div>
            <div class="text-accent-blue/60 font-mono text-sm mt-2">VERIFYING ACCESS PERMISSIONS</div>
        </div>
    </div>

    <!-- Main Content -->
    <div x-show="!isLoading"
         x-transition:enter="transition ease-out duration-1000"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         class="min-h-screen flex items-center justify-center p-4 relative z-20">

        <!-- Security Status Bar -->
        <div class="fixed top-4 left-4 right-4 z-30">
            <div class="glass-morphism rounded-lg p-3 flex items-center justify-between text-white/80 text-sm font-mono">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                        <span>SECURE CONNECTION</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-2"></div>
                        <span>ENCRYPTION: AES-256</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-red-400 font-bold">SECURITY LEVEL: </span>
                    <span class="ml-1 text-red-300" x-text="securityLevel"></span>
                </div>
            </div>
        </div>

        <!-- Main Access Card -->
        <div class="glass-morphism rounded-3xl p-8 max-w-md w-full text-center relative overflow-hidden cyber-border hologram-effect">
            <!-- Security Scan Effect -->
            <div class="security-scan"></div>

            <!-- Header Section -->
            <div class="mb-8">
                <!-- Security Badge -->
                <div class="inline-flex items-center bg-gradient-to-r from-red-600 to-red-500 text-white px-4 py-2 rounded-full text-xs font-bold mb-6 animate-pulse-slow">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span class="font-mono">RESTRICTED ACCESS</span>
                </div>

                <!-- Logo with Animation -->
                <div class="relative mb-6">
                    <div class="absolute inset-0 bg-accent-blue/20 rounded-full animate-ping"></div>
                    <img src="{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-24 h-24 mx-auto relative z-10 animate-float digital-noise">
                </div>

                <!-- Title -->
                <h1 class="text-3xl font-bold text-white mb-2 font-mono tracking-wider">
                    ADMIN ACCESS PORTAL
                </h1>
                <p class="text-white/70 font-mono text-sm tracking-wide">
                    MUNICIPALITY OF DUMINGAG
                </p>
                <div class="text-accent-blue/60 font-mono text-xs mt-2">
                    SECURE ADMINISTRATIVE INTERFACE
                </div>
            </div>

            <!-- Warning Section -->
            <div class="bg-gradient-to-r from-amber-500/20 to-orange-500/20 border border-amber-400/30 rounded-xl p-6 mb-8 backdrop-blur-sm">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-amber-400 text-xl animate-pulse"></i>
                    </div>
                    <div class="text-left">
                        <h3 class="font-bold text-amber-300 mb-2 font-mono text-sm">
                            AUTHORIZED PERSONNEL ONLY
                        </h3>
                        <p class="text-amber-200/80 text-xs font-mono leading-relaxed">
                            This area is restricted to authorized municipal staff only.
                            Unauthorized access is prohibited and monitored.
                            All activities are logged and tracked.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Access Button -->
            <div class="mb-8">
                <a href="{% url 'ordinances:admin_login' %}"
                   class="group relative inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-accent-blue to-accent-deep text-white font-bold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-accent-blue/50 animate-glow font-mono tracking-wide"
                   @click="accessAttempts++">
                    <div class="absolute inset-0 bg-gradient-to-r from-accent-blue to-accent-deep rounded-xl blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                    <div class="relative flex items-center">
                        <i class="fas fa-key mr-3 text-lg"></i>
                        <span>ADMIN LOGIN</span>
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </a>
            </div>

            <!-- Security Features -->
            <div class="space-y-3 mb-8">
                <div class="flex items-center justify-center text-white/60 text-xs font-mono">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-3"></div>
                    <i class="fas fa-lock mr-2"></i>
                    <span>SECURE SSL CONNECTION</span>
                </div>
                <div class="flex items-center justify-center text-white/60 text-xs font-mono">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-3"></div>
                    <i class="fas fa-eye mr-2"></i>
                    <span>ACCESS LOGGING ENABLED</span>
                </div>
                <div class="flex items-center justify-center text-white/60 text-xs font-mono">
                    <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse mr-3"></div>
                    <i class="fas fa-shield-virus mr-2"></i>
                    <span>INTRUSION DETECTION ACTIVE</span>
                </div>
            </div>

            <!-- Back to Public Site -->
            <a href="{% url 'ordinances:home' %}"
               class="inline-flex items-center text-white/60 hover:text-white text-sm font-mono transition-all duration-300 hover:bg-white/10 px-4 py-2 rounded-lg border border-white/20 hover:border-white/40">
                <i class="fas fa-arrow-left mr-2"></i>
                <span>BACK TO PUBLIC SITE</span>
            </a>
        </div>

        <!-- Floating Security Elements -->
        <div class="fixed bottom-4 right-4 z-30">
            <div class="glass-morphism rounded-lg p-3 text-white/80 text-xs font-mono">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse mr-2"></div>
                    <span>MONITORING ACTIVE</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Three.js Security Animation Script -->
    <script>
        class SecurityThreeJS {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.particles = null;
                this.securityGrid = null;
                this.canvas = document.getElementById('security-canvas');
                this.animationId = null;
                this.mouseX = 0;
                this.mouseY = 0;

                if (this.canvas && typeof THREE !== 'undefined') {
                    this.init();
                    this.animate();
                    this.setupEventListeners();
                }
            }

            init() {
                // Scene setup
                this.scene = new THREE.Scene();

                // Camera setup
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.z = 5;

                // Renderer setup
                this.renderer = new THREE.WebGLRenderer({
                    canvas: this.canvas,
                    alpha: true,
                    antialias: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x000000, 0);

                // Create security effects
                this.createSecurityParticles();
                this.createSecurityGrid();
                this.createSecurityBeams();

                // Handle resize
                window.addEventListener('resize', () => this.onWindowResize());
            }

            createSecurityParticles() {
                const particleCount = 200;
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(particleCount * 3);
                const colors = new Float32Array(particleCount * 3);
                const sizes = new Float32Array(particleCount);

                // Security color palette
                const securityColors = [
                    new THREE.Color(0x2563eb), // Blue
                    new THREE.Color(0x00ffff), // Cyan
                    new THREE.Color(0x0080ff), // Light blue
                    new THREE.Color(0x4080ff), // Medium blue
                ];

                for (let i = 0; i < particleCount; i++) {
                    const i3 = i * 3;

                    // Position
                    positions[i3] = (Math.random() - 0.5) * 20;
                    positions[i3 + 1] = (Math.random() - 0.5) * 20;
                    positions[i3 + 2] = (Math.random() - 0.5) * 10;

                    // Color
                    const color = securityColors[Math.floor(Math.random() * securityColors.length)];
                    colors[i3] = color.r;
                    colors[i3 + 1] = color.g;
                    colors[i3 + 2] = color.b;

                    // Size
                    sizes[i] = Math.random() * 2 + 0.5;
                }

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

                const material = new THREE.PointsMaterial({
                    size: 2,
                    vertexColors: true,
                    transparent: true,
                    opacity: 0.6,
                    blending: THREE.AdditiveBlending
                });

                this.particles = new THREE.Points(geometry, material);
                this.scene.add(this.particles);
            }

            createSecurityGrid() {
                const geometry = new THREE.PlaneGeometry(30, 30, 20, 20);
                const material = new THREE.MeshBasicMaterial({
                    color: 0x2563eb,
                    transparent: true,
                    opacity: 0.1,
                    wireframe: true,
                    blending: THREE.AdditiveBlending
                });

                this.securityGrid = new THREE.Mesh(geometry, material);
                this.securityGrid.position.z = -5;
                this.securityGrid.rotation.x = Math.PI / 4;
                this.scene.add(this.securityGrid);
            }

            createSecurityBeams() {
                for (let i = 0; i < 5; i++) {
                    const geometry = new THREE.CylinderGeometry(0.02, 0.02, 20, 8);
                    const material = new THREE.MeshBasicMaterial({
                        color: 0x00ffff,
                        transparent: true,
                        opacity: 0.3,
                        emissive: 0x004444
                    });

                    const beam = new THREE.Mesh(geometry, material);
                    beam.position.x = (Math.random() - 0.5) * 15;
                    beam.position.z = (Math.random() - 0.5) * 10;
                    beam.rotation.z = Math.random() * Math.PI;

                    this.scene.add(beam);
                }
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                const time = Date.now() * 0.001;

                // Animate particles
                if (this.particles) {
                    this.particles.rotation.y = time * 0.1;

                    const positions = this.particles.geometry.attributes.position.array;
                    for (let i = 0; i < positions.length; i += 3) {
                        positions[i + 1] += Math.sin(time * 2 + positions[i] * 0.1) * 0.01;
                        positions[i] += Math.cos(time * 1.5 + positions[i + 2] * 0.1) * 0.005;
                    }
                    this.particles.geometry.attributes.position.needsUpdate = true;
                }

                // Animate security grid
                if (this.securityGrid) {
                    this.securityGrid.rotation.z = time * 0.05;

                    const vertices = this.securityGrid.geometry.attributes.position.array;
                    for (let i = 0; i < vertices.length; i += 3) {
                        vertices[i + 2] = Math.sin(time + vertices[i] * 0.1 + vertices[i + 1] * 0.1) * 0.3;
                    }
                    this.securityGrid.geometry.attributes.position.needsUpdate = true;
                }

                // Mouse interaction
                if (this.particles) {
                    this.particles.rotation.x = this.mouseY * 0.1;
                    this.particles.rotation.y = this.mouseX * 0.1;
                }

                this.renderer.render(this.scene, this.camera);
            }

            setupEventListeners() {
                document.addEventListener('mousemove', (event) => {
                    this.mouseX = (event.clientX / window.innerWidth) * 2 - 1;
                    this.mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
                });
            }

            onWindowResize() {
                if (!this.camera || !this.renderer) return;

                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }
        }

        // Initialize security system
        document.addEventListener('DOMContentLoaded', function() {
            // Log access attempt
            console.log('🔒 Admin access portal accessed at:', new Date().toISOString());

            // Initialize Three.js security animation
            new SecurityThreeJS();

            // Security monitoring simulation
            setInterval(() => {
                console.log('🛡️ Security scan completed - All systems secure');
            }, 10000);
        });
    </script>
</body>
</html>
