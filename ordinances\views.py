from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
from django.utils import timezone
from django.http import JsonResponse
from .models import Ordinance, Category, Sponsor, Attachment, OrdinanceLog, Official, Banner
import json

User = get_user_model()


def home(request):
    """Home page with featured ordinances and officials"""
    recent_ordinances = Ordinance.objects.filter(
        status__in=['approved', 'published']
    ).select_related('category').prefetch_related('sponsors')[:6]

    categories = Category.objects.all()

    # Get statistics for the home page
    total_ordinances = Ordinance.objects.filter(status__in=['approved', 'published']).count()
    total_categories = categories.count()

    # Get top sponsors (council members)
    top_sponsors = Sponsor.objects.annotate(
        ordinance_count=Count('ordinance', filter=Q(ordinance__status__in=['approved', 'published']))
    ).filter(ordinance_count__gt=0).order_by('-ordinance_count')[:8]

    # Get officials from database
    officials = {
        'mayor': Official.objects.filter(position='mayor', status='active').first(),
        'vice_mayor': Official.objects.filter(position='vice_mayor', status='active').first(),
        'council_members': Official.objects.filter(position='councilor', status='active').order_by('order', 'name')
    }

    # Get active banners
    active_banners = Banner.get_active_banners()

    # Get current date information for notices section
    from datetime import datetime
    now = datetime.now()
    current_year = now.year
    current_month = now.month

    # Calculate additional metrics for notices section
    new_ordinances_count = Ordinance.objects.filter(
        created_at__year=current_year,
        created_at__month=current_month
    ).count()

    updated_ordinances_count = Ordinance.objects.filter(
        updated_at__year=current_year,
        updated_at__month=current_month
    ).exclude(created_at__year=current_year, created_at__month=current_month).count()

    context = {
        'recent_ordinances': recent_ordinances,
        'categories': categories,
        'total_ordinances': total_ordinances,
        'total_categories': total_categories,
        'top_sponsors': top_sponsors,
        'officials': officials,
        'active_banners': active_banners,
        'current_year': current_year,
        'current_month': current_month,
        'new_ordinances_count': new_ordinances_count,
        'updated_ordinances_count': updated_ordinances_count,
    }
    return render(request, 'ordinances/home.html', context)


def ordinance_list(request):
    """List all public ordinances with search and filtering"""
    ordinances = Ordinance.objects.filter(
        status__in=['approved', 'published']
    ).select_related('category').prefetch_related('sponsors')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        ordinances = Ordinance.search(search_query).filter(
            status__in=['approved', 'published']
        )

    # Filter by category
    category_slug = request.GET.get('category', '')
    if category_slug:
        ordinances = ordinances.filter(category__slug=category_slug)

    # Filter by year
    year = request.GET.get('year', '')
    if year:
        try:
            year = int(year)
            ordinances = ordinances.filter(year_passed=year)
        except ValueError:
            pass

    # Filter by status
    status = request.GET.get('status', '')
    if status and status in ['approved', 'published']:
        ordinances = ordinances.filter(status=status)

    # Pagination
    paginator = Paginator(ordinances, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    categories = Category.objects.all()
    years = Ordinance.objects.filter(
        status__in=['approved', 'published']
    ).values_list('year_passed', flat=True).distinct().order_by('-year_passed')

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'years': years,
        'search_query': search_query,
        'selected_category': category_slug,
        'selected_year': year,
        'selected_status': status,
    }

    # HTMX request - return partial template
    if request.headers.get('HX-Request'):
        return render(request, 'ordinances/partials/ordinance_list.html', context)

    return render(request, 'ordinances/ordinance_list.html', context)


def ordinance_detail(request, slug):
    """Detail view for a single ordinance"""
    ordinance = get_object_or_404(
        Ordinance.objects.select_related('category', 'created_by', 'updated_by')
        .prefetch_related('sponsors', 'attachments'),
        slug=slug,
        status__in=['approved', 'published']
    )

    # Get related ordinances
    related_ordinances = Ordinance.objects.filter(
        category=ordinance.category,
        status__in=['approved', 'published']
    ).exclude(id=ordinance.id)[:3]

    context = {
        'ordinance': ordinance,
        'related_ordinances': related_ordinances,
    }
    return render(request, 'ordinances/ordinance_detail.html', context)


def search_suggestions(request):
    """AJAX endpoint for search suggestions"""
    query = request.GET.get('q', '')
    suggestions = []

    if len(query) >= 2:
        ordinances = Ordinance.objects.filter(
            Q(title__icontains=query) | Q(ordinance_number__icontains=query),
            status__in=['approved', 'published']
        )[:5]

        suggestions = [
            {
                'title': ordinance.title,
                'ordinance_number': ordinance.ordinance_number,
                'url': ordinance.get_absolute_url()
            }
            for ordinance in ordinances
        ]

    return JsonResponse({'suggestions': suggestions})


# Admin Views (require authentication)

@login_required
def admin_dashboard(request):
    """Comprehensive admin dashboard with advanced analytics"""
    from django.db.models import Count, Q
    from django.utils import timezone
    from datetime import datetime, timedelta
    import json

    # Basic statistics
    total_ordinances = Ordinance.objects.count()
    draft_ordinances = Ordinance.objects.filter(status='draft').count()
    reviewed_ordinances = Ordinance.objects.filter(status='reviewed').count()
    approved_ordinances = Ordinance.objects.filter(status='approved').count()
    published_ordinances = Ordinance.objects.filter(status='published').count()
    archived_ordinances = Ordinance.objects.filter(is_archived=True).count()

    # Recent activity (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_ordinances_count = Ordinance.objects.filter(created_at__gte=thirty_days_ago).count()
    recent_updates_count = Ordinance.objects.filter(updated_at__gte=thirty_days_ago).count()

    # Category statistics
    category_stats = Category.objects.annotate(
        ordinance_count=Count('ordinances'),
        published_count=Count('ordinances', filter=Q(ordinances__status='published')),
        draft_count=Count('ordinances', filter=Q(ordinances__status='draft'))
    ).order_by('-ordinance_count')

    # Monthly ordinance creation data for charts (last 12 months)
    monthly_data = []
    for i in range(12):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        count = Ordinance.objects.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()
        monthly_data.append({
            'month': month_start.strftime('%b %Y'),
            'count': count
        })
    monthly_data.reverse()

    # Status distribution for pie chart
    status_distribution = [
        {'status': 'Draft', 'count': draft_ordinances, 'color': '#6B7280'},
        {'status': 'Reviewed', 'count': reviewed_ordinances, 'color': '#3B82F6'},
        {'status': 'Approved', 'count': approved_ordinances, 'color': '#F59E0B'},
        {'status': 'Published', 'count': published_ordinances, 'color': '#10B981'},
    ]

    # Year-wise statistics
    current_year = timezone.now().year
    yearly_stats = []
    for year in range(current_year - 4, current_year + 1):
        year_count = Ordinance.objects.filter(year_passed=year).count()
        yearly_stats.append({'year': year, 'count': year_count})

    # Top sponsors by ordinance count
    top_sponsors = Sponsor.objects.annotate(
        ordinance_count=Count('ordinance')
    ).order_by('-ordinance_count')[:5]

    # Recent activity logs
    recent_logs = OrdinanceLog.objects.select_related('ordinance', 'user').order_by('-timestamp')[:10]

    # Recent ordinances
    recent_ordinances = Ordinance.objects.select_related('category').prefetch_related('sponsors')[:10]

    # Pending actions (ordinances that need attention)
    pending_review = Ordinance.objects.filter(status='draft').count()
    pending_approval = Ordinance.objects.filter(status='reviewed').count()
    pending_publication = Ordinance.objects.filter(status='approved').count()

    # System health metrics
    total_attachments = Attachment.objects.count()
    total_categories = Category.objects.count()
    total_sponsors = Sponsor.objects.count()
    total_logs = OrdinanceLog.objects.count()

    # Get officials for dashboard
    officials = {
        'mayor': Official.objects.filter(position='mayor', status='active').first(),
        'vice_mayor': Official.objects.filter(position='vice_mayor', status='active').first(),
        'council_members': Official.objects.filter(position='councilor', status='active').order_by('order', 'name')[:4]
    }

    # Get data for modals
    categories = Category.objects.all()
    sponsors = Sponsor.objects.all()
    years = list(range(current_year - 5, current_year + 2))

    context = {
        # Basic stats
        'total_ordinances': total_ordinances,
        'draft_ordinances': draft_ordinances,
        'reviewed_ordinances': reviewed_ordinances,
        'approved_ordinances': approved_ordinances,
        'published_ordinances': published_ordinances,
        'archived_ordinances': archived_ordinances,

        # Recent activity
        'recent_ordinances_count': recent_ordinances_count,
        'recent_updates_count': recent_updates_count,

        # Analytics data
        'category_stats': category_stats,
        'monthly_data': json.dumps(monthly_data),
        'status_distribution': json.dumps(status_distribution),
        'yearly_stats': yearly_stats,
        'top_sponsors': top_sponsors,

        # Recent data
        'recent_logs': recent_logs,
        'recent_ordinances': recent_ordinances,

        # Pending actions
        'pending_review': pending_review,
        'pending_approval': pending_approval,
        'pending_publication': pending_publication,

        # System metrics
        'total_attachments': total_attachments,
        'total_categories': total_categories,
        'total_sponsors': total_sponsors,
        'total_logs': total_logs,

        # Calculated metrics
        'completion_rate': round((published_ordinances / total_ordinances * 100) if total_ordinances > 0 else 0, 1),
        'draft_rate': round((draft_ordinances / total_ordinances * 100) if total_ordinances > 0 else 0, 1),

        # Officials data
        'officials': officials,

        # Modal data
        'categories': categories,
        'sponsors': sponsors,
        'current_year': current_year,
        'years': years,
    }
    return render(request, 'ordinances/admin/dashboard.html', context)


@login_required
def admin_ordinance_list(request):
    """Admin view for all ordinances"""
    ordinances = Ordinance.objects.select_related('category').prefetch_related('sponsors')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        ordinances = Ordinance.search(search_query)

    # Filter by status (admin can see all statuses)
    status = request.GET.get('status', '')
    if status:
        ordinances = ordinances.filter(status=status)

    # Filter by category
    category_slug = request.GET.get('category', '')
    if category_slug:
        ordinances = ordinances.filter(category__slug=category_slug)

    # Pagination
    paginator = Paginator(ordinances, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    categories = Category.objects.all()

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_slug,
        'selected_status': status,
        'status_choices': Ordinance.STATUS_CHOICES,
    }

    # HTMX request - return partial template
    if request.headers.get('HX-Request'):
        return render(request, 'ordinances/admin/partials/ordinance_list.html', context)

    return render(request, 'ordinances/admin/ordinance_list.html', context)


@login_required
@require_http_methods(["POST"])
def update_ordinance_status(request, ordinance_id):
    """HTMX endpoint to update ordinance status"""
    ordinance = get_object_or_404(Ordinance, id=ordinance_id)
    new_status = request.POST.get('status')

    if new_status in dict(Ordinance.STATUS_CHOICES):
        old_status = ordinance.status
        ordinance.status = new_status
        ordinance.updated_by = request.user
        ordinance.save()

        # Log the action
        from .models import OrdinanceLog
        OrdinanceLog.objects.create(
            ordinance=ordinance,
            user=request.user,
            action=f'Status changed from {old_status} to {new_status}',
            notes=f'Status updated via admin interface'
        )

        messages.success(request, f'Ordinance status updated to {new_status}')

        # Return updated status badge
        context = {'ordinance': ordinance}
        return render(request, 'ordinances/admin/partials/status_badge.html', context)

    return JsonResponse({'error': 'Invalid status'}, status=400)


def export_ordinance_pdf(request, slug):
    """Export ordinance as PDF"""
    ordinance = get_object_or_404(
        Ordinance,
        slug=slug,
        status__in=['approved', 'published']
    )

    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from io import BytesIO

        # Create PDF buffer
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        content = []

        # Title
        content.append(Paragraph(f"ORDINANCE NO. {ordinance.ordinance_number}", title_style))
        content.append(Paragraph(ordinance.title, title_style))
        content.append(Spacer(1, 20))

        # Metadata
        content.append(Paragraph(f"<b>Category:</b> {ordinance.category}", styles['Normal']))
        content.append(Paragraph(f"<b>Year Passed:</b> {ordinance.year_passed}", styles['Normal']))
        content.append(Paragraph(f"<b>Status:</b> {ordinance.get_status_display()}", styles['Normal']))

        if ordinance.sponsors.exists():
            sponsors = ", ".join([str(sponsor) for sponsor in ordinance.sponsors.all()])
            content.append(Paragraph(f"<b>Sponsors:</b> {sponsors}", styles['Normal']))

        content.append(Spacer(1, 20))

        # Content
        content.append(Paragraph("<b>CONTENT:</b>", styles['Heading2']))
        content.append(Spacer(1, 10))

        # Split content into paragraphs
        for paragraph in ordinance.content.split('\n\n'):
            if paragraph.strip():
                content.append(Paragraph(paragraph.strip(), styles['Normal']))
                content.append(Spacer(1, 10))

        # Build PDF
        doc.build(content)

        # Return PDF response
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="ordinance_{ordinance.ordinance_number}.pdf"'

        return response

    except ImportError:
        messages.error(request, 'PDF export is not available. Please install reportlab.')
        return redirect('ordinance_detail', slug=slug)
    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('ordinance_detail', slug=slug)


@login_required
def admin_analytics(request):
    """Advanced analytics page for admin"""
    from django.db.models import Count, Q, Avg
    from django.utils import timezone
    from datetime import timedelta
    import json

    # Time-based analytics
    now = timezone.now()
    last_30_days = now - timedelta(days=30)
    last_90_days = now - timedelta(days=90)
    last_year = now - timedelta(days=365)

    # Activity trends
    activity_data = []
    for i in range(30):
        date = now - timedelta(days=i)
        day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        day_end = day_start + timedelta(days=1)

        created_count = Ordinance.objects.filter(
            created_at__gte=day_start,
            created_at__lt=day_end
        ).count()

        updated_count = Ordinance.objects.filter(
            updated_at__gte=day_start,
            updated_at__lt=day_end
        ).exclude(created_at__gte=day_start).count()

        activity_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'created': created_count,
            'updated': updated_count
        })

    activity_data.reverse()

    # Category performance
    category_performance = Category.objects.annotate(
        total_ordinances=Count('ordinances'),
        published_ordinances=Count('ordinances', filter=Q(ordinances__status='published')),
        draft_ordinances=Count('ordinances', filter=Q(ordinances__status='draft')),
    ).order_by('-total_ordinances')

    # User activity
    user_activity = User.objects.filter(
        Q(created_ordinances__isnull=False) | Q(updated_ordinances__isnull=False)
    ).annotate(
        created_count=Count('created_ordinances'),
        updated_count=Count('updated_ordinances'),
        total_activity=Count('created_ordinances') + Count('updated_ordinances')
    ).order_by('-total_activity')[:10]

    # Status transition analytics
    status_transitions = OrdinanceLog.objects.filter(
        action__icontains='Status changed'
    ).values('action').annotate(count=Count('id')).order_by('-count')

    context = {
        'activity_data': json.dumps(activity_data),
        'category_performance': category_performance,
        'user_activity': user_activity,
        'status_transitions': status_transitions,
        'last_30_days_count': Ordinance.objects.filter(created_at__gte=last_30_days).count(),
        'last_90_days_count': Ordinance.objects.filter(created_at__gte=last_90_days).count(),
        'last_year_count': Ordinance.objects.filter(created_at__gte=last_year).count(),
    }

    return render(request, 'ordinances/admin/analytics.html', context)


@login_required
def admin_ordinance_create(request):
    """Create new ordinance"""
    if request.method == 'POST':
        # Handle form submission
        ordinance_number = request.POST.get('ordinance_number')
        title = request.POST.get('title')
        content = request.POST.get('content')
        category_id = request.POST.get('category')
        year_passed = request.POST.get('year_passed')
        status = request.POST.get('status', 'draft')
        sponsor_ids = request.POST.getlist('sponsors')

        # Validation
        if not all([ordinance_number, title, content, year_passed]):
            messages.error(request, 'Please fill in all required fields.')
            return redirect('ordinances:admin_ordinance_create')

        try:
            # Create ordinance
            ordinance = Ordinance.objects.create(
                ordinance_number=ordinance_number,
                title=title,
                content=content,
                category_id=category_id if category_id else None,
                year_passed=int(year_passed),
                status=status,
                created_by=request.user,
                updated_by=request.user
            )

            # Add sponsors
            if sponsor_ids:
                ordinance.sponsors.set(sponsor_ids)

            # Log the action
            OrdinanceLog.objects.create(
                ordinance=ordinance,
                user=request.user,
                action='Created ordinance',
                notes='Ordinance created via custom admin interface'
            )

            messages.success(request, f'Ordinance {ordinance_number} created successfully!')
            return redirect('ordinances:admin_ordinance_edit', ordinance_id=ordinance.id)

        except Exception as e:
            messages.error(request, f'Error creating ordinance: {str(e)}')

    # Get form data
    categories = Category.objects.all()
    sponsors = Sponsor.objects.all()
    current_year = timezone.now().year
    years = list(range(current_year - 5, current_year + 2))

    context = {
        'categories': categories,
        'sponsors': sponsors,
        'years': years,
        'status_choices': Ordinance.STATUS_CHOICES,
    }
    return render(request, 'ordinances/admin/ordinance_form.html', context)


@login_required
def admin_ordinance_edit(request, ordinance_id):
    """Edit existing ordinance"""
    ordinance = get_object_or_404(Ordinance, id=ordinance_id)

    if request.method == 'POST':
        # Handle form submission
        old_data = {
            'ordinance_number': ordinance.ordinance_number,
            'title': ordinance.title,
            'content': ordinance.content,
            'status': ordinance.status,
        }

        ordinance.ordinance_number = request.POST.get('ordinance_number')
        ordinance.title = request.POST.get('title')
        ordinance.content = request.POST.get('content')
        category_id = request.POST.get('category')
        ordinance.category_id = category_id if category_id else None
        ordinance.year_passed = int(request.POST.get('year_passed'))
        ordinance.status = request.POST.get('status', 'draft')
        ordinance.updated_by = request.user
        sponsor_ids = request.POST.getlist('sponsors')

        try:
            ordinance.save()

            # Update sponsors
            ordinance.sponsors.set(sponsor_ids)

            # Log changes
            changes = []
            for field, old_value in old_data.items():
                new_value = getattr(ordinance, field)
                if old_value != new_value:
                    changes.append(f'{field}: {old_value} → {new_value}')

            if changes:
                OrdinanceLog.objects.create(
                    ordinance=ordinance,
                    user=request.user,
                    action='Updated ordinance',
                    notes=f'Changes: {", ".join(changes)}'
                )

            messages.success(request, f'Ordinance {ordinance.ordinance_number} updated successfully!')
            return redirect('ordinances:admin_ordinance_edit', ordinance_id=ordinance.id)

        except Exception as e:
            messages.error(request, f'Error updating ordinance: {str(e)}')

    # Get form data
    categories = Category.objects.all()
    sponsors = Sponsor.objects.all()
    current_year = timezone.now().year
    years = list(range(current_year - 10, current_year + 2))

    context = {
        'ordinance': ordinance,
        'categories': categories,
        'sponsors': sponsors,
        'years': years,
        'status_choices': Ordinance.STATUS_CHOICES,
    }
    return render(request, 'ordinances/admin/ordinance_form.html', context)


@login_required
def admin_ordinance_delete(request, ordinance_id):
    """Delete ordinance"""
    ordinance = get_object_or_404(Ordinance, id=ordinance_id)

    if request.method == 'POST':
        ordinance_number = ordinance.ordinance_number
        ordinance.delete()
        messages.success(request, f'Ordinance {ordinance_number} deleted successfully!')
        return redirect('ordinances:admin_ordinance_list')

    context = {'ordinance': ordinance}
    return render(request, 'ordinances/admin/ordinance_delete.html', context)


@login_required
def admin_category_list(request):
    """List all categories"""
    categories = Category.objects.annotate(
        ordinance_count=Count('ordinances')
    ).order_by('name')

    context = {'categories': categories}
    return render(request, 'ordinances/admin/category_list.html', context)


@login_required
def admin_category_create(request):
    """Create new category"""
    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description', '')

        if not name:
            messages.error(request, 'Category name is required.')
            return redirect('ordinances:admin_category_create')

        try:
            category = Category.objects.create(
                name=name,
                description=description
            )
            messages.success(request, f'Category "{name}" created successfully!')
            return redirect('ordinances:admin_category_list')
        except Exception as e:
            messages.error(request, f'Error creating category: {str(e)}')

    return render(request, 'ordinances/admin/category_form.html')


@login_required
def admin_category_edit(request, category_id):
    """Edit existing category"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        category.name = request.POST.get('name')
        category.description = request.POST.get('description', '')

        try:
            category.save()
            messages.success(request, f'Category "{category.name}" updated successfully!')
            return redirect('ordinances:admin_category_list')
        except Exception as e:
            messages.error(request, f'Error updating category: {str(e)}')

    context = {'category': category}
    return render(request, 'ordinances/admin/category_form.html', context)


@login_required
def admin_category_delete(request, category_id):
    """Delete category"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        category_name = category.name
        category.delete()
        messages.success(request, f'Category "{category_name}" deleted successfully!')
        return redirect('ordinances:admin_category_list')

    context = {'category': category}
    return render(request, 'ordinances/admin/category_delete.html', context)


# Official Management Views
@login_required
def admin_official_list(request):
    """List all officials with filtering and search"""
    from django.db.models import Count, Q

    officials = Official.objects.all().order_by('order', 'position', 'name')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        officials = officials.filter(
            Q(name__icontains=search_query) |
            Q(committee__icontains=search_query) |
            Q(bio__icontains=search_query)
        )

    # Filter by status
    status = request.GET.get('status', '')
    if status:
        officials = officials.filter(status=status)

    # Filter by position
    position = request.GET.get('position', '')
    if position:
        officials = officials.filter(position=position)

    # Statistics
    stats = {
        'total': Official.objects.count(),
        'active': Official.objects.filter(status='active').count(),
        'inactive': Official.objects.filter(status='inactive').count(),
        'retired': Official.objects.filter(status='retired').count(),
    }

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(officials, 12)  # 12 officials per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'officials': page_obj,
        'page_obj': page_obj,
        'stats': stats,
        'search_query': search_query,
        'selected_status': status,
        'selected_position': position,
    }

    return render(request, 'ordinances/admin/official_list.html', context)


@login_required
def admin_official_create(request):
    """Create new official"""
    if request.method == 'POST':
        # Handle form submission
        name = request.POST.get('name')
        position = request.POST.get('position')
        committee = request.POST.get('committee', '')
        bio = request.POST.get('bio', '')
        term_start = request.POST.get('term_start')
        term_end = request.POST.get('term_end')
        status = request.POST.get('status', 'active')
        order = request.POST.get('order', 0)
        profile_picture = request.FILES.get('profile_picture')

        # Handle achievements
        achievements_list = request.POST.getlist('achievements[]')
        achievements = '\n'.join([a.strip() for a in achievements_list if a.strip()])

        # Validation
        if not all([name, position, term_start, term_end]):
            messages.error(request, 'Please fill in all required fields.')
            return redirect('ordinances:admin_official_create')

        try:
            # Create official
            official = Official.objects.create(
                name=name,
                position=position,
                committee=committee,
                bio=bio,
                achievements=achievements,
                term_start=term_start,
                term_end=term_end,
                status=status,
                order=int(order) if order else 0,
                profile_picture=profile_picture
            )

            messages.success(request, f'Official "{name}" created successfully!')
            return redirect('ordinances:admin_official_view', official_id=official.id)

        except Exception as e:
            messages.error(request, f'Error creating official: {str(e)}')

    # Get form data
    context = {
        'position_choices': Official.POSITION_CHOICES,
        'status_choices': Official.STATUS_CHOICES,
    }
    return render(request, 'ordinances/admin/official_form.html', context)


@login_required
def admin_official_create_ajax(request):
    """AJAX endpoint for creating new official"""
    from datetime import datetime

    if request.method == 'POST':
        try:
            # Handle form submission
            name = request.POST.get('name')
            position = request.POST.get('position')
            committee = request.POST.get('committee', '')
            bio = request.POST.get('bio', '')
            term_start = request.POST.get('term_start')
            term_end = request.POST.get('term_end')
            status = request.POST.get('status', 'active')
            order = request.POST.get('order', 0)
            profile_picture = request.FILES.get('profile_picture')

            # Handle achievements
            achievements_list = request.POST.getlist('achievements[]')
            achievements = '\n'.join([a.strip() for a in achievements_list if a.strip()])

            # Validation
            if not all([name, position, term_start, term_end]):
                return JsonResponse({
                    'success': False,
                    'message': 'Please fill in all required fields.'
                })

            # Validate term dates
            try:
                start_date = datetime.strptime(term_start, '%Y-%m-%d').date()
                end_date = datetime.strptime(term_end, '%Y-%m-%d').date()

                if start_date >= end_date:
                    return JsonResponse({
                        'success': False,
                        'message': 'Term end date must be after term start date.'
                    })
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid date format.'
                })

            # Create official
            official = Official.objects.create(
                name=name,
                position=position,
                committee=committee,
                bio=bio,
                achievements=achievements,
                term_start=term_start,
                term_end=term_end,
                status=status,
                order=int(order) if order else 0,
                profile_picture=profile_picture
            )

            return JsonResponse({
                'success': True,
                'message': f'Official "{name}" created successfully!',
                'official_id': official.id
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error creating official: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method.'
    })


@login_required
def admin_ordinance_create_ajax(request):
    """AJAX endpoint for creating new ordinance"""
    if request.method == 'POST':
        try:
            # Handle form submission
            ordinance_number = request.POST.get('ordinance_number')
            title = request.POST.get('title')
            content = request.POST.get('content')
            category_id = request.POST.get('category')
            year_passed = request.POST.get('year_passed')
            status = request.POST.get('status', 'draft')
            sponsor_ids = request.POST.getlist('sponsors')

            # Validation
            if not all([ordinance_number, title, content, year_passed]):
                return JsonResponse({
                    'success': False,
                    'message': 'Please fill in all required fields.'
                })

            try:
                # Create ordinance
                ordinance = Ordinance.objects.create(
                    ordinance_number=ordinance_number,
                    title=title,
                    content=content,
                    category_id=category_id if category_id else None,
                    year_passed=int(year_passed),
                    status=status,
                    created_by=request.user,
                    updated_by=request.user
                )

                # Add sponsors
                if sponsor_ids:
                    ordinance.sponsors.set(sponsor_ids)

                # Log the action
                OrdinanceLog.objects.create(
                    ordinance=ordinance,
                    user=request.user,
                    action='Created ordinance',
                    notes='Ordinance created via modal interface'
                )

                return JsonResponse({
                    'success': True,
                    'message': f'Ordinance "{ordinance_number}" created successfully!',
                    'ordinance_id': ordinance.id
                })

            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'Error creating ordinance: {str(e)}'
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error processing request: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method.'
    })


@login_required
def admin_category_create_ajax(request):
    """AJAX endpoint for creating new category"""
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description', '')

            if not name:
                return JsonResponse({
                    'success': False,
                    'message': 'Category name is required.'
                })

            # Create category
            category = Category.objects.create(
                name=name,
                description=description
            )

            return JsonResponse({
                'success': True,
                'message': f'Category "{name}" created successfully!',
                'category_id': category.id
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error creating category: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method.'
    })


@login_required
def admin_sponsor_create_ajax(request):
    """AJAX endpoint for creating new sponsor"""
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            position = request.POST.get('position')

            if not all([name, position]):
                return JsonResponse({
                    'success': False,
                    'message': 'Name and position are required.'
                })

            # Create sponsor
            sponsor = Sponsor.objects.create(
                name=name,
                position=position
            )

            return JsonResponse({
                'success': True,
                'message': f'Sponsor "{name}" created successfully!',
                'sponsor_id': sponsor.id
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'Error creating sponsor: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method.'
    })


@login_required
def admin_official_edit(request, official_id):
    """Edit existing official"""
    official = get_object_or_404(Official, id=official_id)

    if request.method == 'POST':
        # Handle form submission
        official.name = request.POST.get('name')
        official.position = request.POST.get('position')
        official.committee = request.POST.get('committee', '')
        official.bio = request.POST.get('bio', '')
        official.term_start = request.POST.get('term_start')
        official.term_end = request.POST.get('term_end')
        official.status = request.POST.get('status', 'active')
        official.order = int(request.POST.get('order', 0)) if request.POST.get('order') else 0

        # Handle profile picture
        if 'profile_picture' in request.FILES:
            official.profile_picture = request.FILES['profile_picture']

        # Handle achievements
        achievements_list = request.POST.getlist('achievements[]')
        official.achievements = '\n'.join([a.strip() for a in achievements_list if a.strip()])

        try:
            official.save()
            messages.success(request, f'Official "{official.name}" updated successfully!')
            return redirect('ordinances:admin_official_view', official_id=official.id)

        except Exception as e:
            messages.error(request, f'Error updating official: {str(e)}')

    context = {
        'official': official,
        'position_choices': Official.POSITION_CHOICES,
        'status_choices': Official.STATUS_CHOICES,
    }
    return render(request, 'ordinances/admin/official_form.html', context)


@login_required
def admin_official_view(request, official_id):
    """View official details"""
    official = get_object_or_404(Official, id=official_id)

    context = {
        'official': official,
    }
    return render(request, 'ordinances/admin/official_view.html', context)


@login_required
def admin_official_delete(request, official_id):
    """Delete official"""
    official = get_object_or_404(Official, id=official_id)

    if request.method == 'POST':
        official_name = official.name
        official.delete()
        messages.success(request, f'Official "{official_name}" deleted successfully!')
        return redirect('ordinances:admin_official_list')

    context = {'official': official}
    return render(request, 'ordinances/admin/official_delete.html', context)


@login_required
def admin_sponsor_list(request):
    """List all sponsors"""
    sponsors = Sponsor.objects.annotate(
        ordinance_count=Count('ordinance')
    ).order_by('name')

    context = {'sponsors': sponsors}
    return render(request, 'ordinances/admin/sponsor_list.html', context)


@login_required
def admin_sponsor_create(request):
    """Create new sponsor"""
    if request.method == 'POST':
        name = request.POST.get('name')
        position = request.POST.get('position')

        if not all([name, position]):
            messages.error(request, 'Name and position are required.')
            return redirect('ordinances:admin_sponsor_create')

        try:
            Sponsor.objects.create(
                name=name,
                position=position
            )
            messages.success(request, f'Sponsor "{name}" created successfully!')
            return redirect('ordinances:admin_sponsor_list')
        except Exception as e:
            messages.error(request, f'Error creating sponsor: {str(e)}')

    return render(request, 'ordinances/admin/sponsor_form.html')


@login_required
def admin_sponsor_edit(request, sponsor_id):
    """Edit existing sponsor"""
    sponsor = get_object_or_404(Sponsor, id=sponsor_id)

    if request.method == 'POST':
        sponsor.name = request.POST.get('name')
        sponsor.position = request.POST.get('position')

        try:
            sponsor.save()
            messages.success(request, f'Sponsor "{sponsor.name}" updated successfully!')
            return redirect('ordinances:admin_sponsor_list')
        except Exception as e:
            messages.error(request, f'Error updating sponsor: {str(e)}')

    context = {'sponsor': sponsor}
    return render(request, 'ordinances/admin/sponsor_form.html', context)


@login_required
def admin_sponsor_delete(request, sponsor_id):
    """Delete sponsor"""
    sponsor = get_object_or_404(Sponsor, id=sponsor_id)

    if request.method == 'POST':
        sponsor_name = sponsor.name
        sponsor.delete()
        messages.success(request, f'Sponsor "{sponsor_name}" deleted successfully!')
        return redirect('ordinances:admin_sponsor_list')

    context = {'sponsor': sponsor}
    return render(request, 'ordinances/admin/sponsor_delete.html', context)


# Banner Management Views

@login_required
def admin_banner_list(request):
    """List all banners with filtering and search"""
    banners = Banner.objects.all().order_by('-priority', '-created_at')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        banners = banners.filter(
            Q(title__icontains=search_query) |
            Q(message__icontains=search_query)
        )

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter == 'active':
        banners = banners.filter(is_active=True)
    elif status_filter == 'inactive':
        banners = banners.filter(is_active=False)

    # Filter by banner type
    type_filter = request.GET.get('type', '')
    if type_filter:
        banners = banners.filter(banner_type=type_filter)

    context = {
        'banners': banners,
        'search_query': search_query,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'banner_types': Banner.BANNER_TYPES,
    }
    return render(request, 'ordinances/admin/banner_list.html', context)


@login_required
def admin_banner_create(request):
    """Create new banner"""
    if request.method == 'POST':
        title = request.POST.get('title')
        message = request.POST.get('message')
        banner_type = request.POST.get('banner_type')
        color_scheme = request.POST.get('color_scheme')
        is_active = request.POST.get('is_active') == 'on'
        is_dismissible = request.POST.get('is_dismissible') == 'on'
        show_icon = request.POST.get('show_icon') == 'on'
        priority = request.POST.get('priority', 1)
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')

        if not all([title, message, banner_type, color_scheme]):
            messages.error(request, 'Title, message, banner type, and color scheme are required.')
            return redirect('ordinances:admin_banner_create')

        try:
            # Parse dates
            start_datetime = timezone.now()
            if start_date:
                start_datetime = timezone.datetime.strptime(start_date, '%Y-%m-%dT%H:%M')
                start_datetime = timezone.make_aware(start_datetime)

            end_datetime = None
            if end_date:
                end_datetime = timezone.datetime.strptime(end_date, '%Y-%m-%dT%H:%M')
                end_datetime = timezone.make_aware(end_datetime)

            banner = Banner.objects.create(
                title=title,
                message=message,
                banner_type=banner_type,
                color_scheme=color_scheme,
                is_active=is_active,
                is_dismissible=is_dismissible,
                show_icon=show_icon,
                priority=int(priority),
                start_date=start_datetime,
                end_date=end_datetime,
                created_by=request.user,
                updated_by=request.user
            )

            messages.success(request, f'Banner "{title}" created successfully!')
            return redirect('ordinances:admin_banner_edit', banner_id=banner.id)

        except Exception as e:
            messages.error(request, f'Error creating banner: {str(e)}')

    context = {
        'banner_types': Banner.BANNER_TYPES,
        'color_schemes': Banner.BANNER_COLORS,
    }
    return render(request, 'ordinances/admin/banner_form.html', context)


@login_required
def admin_banner_edit(request, banner_id):
    """Edit existing banner"""
    banner = get_object_or_404(Banner, id=banner_id)

    if request.method == 'POST':
        title = request.POST.get('title')
        message = request.POST.get('message')
        banner_type = request.POST.get('banner_type')
        color_scheme = request.POST.get('color_scheme')
        is_active = request.POST.get('is_active') == 'on'
        is_dismissible = request.POST.get('is_dismissible') == 'on'
        show_icon = request.POST.get('show_icon') == 'on'
        priority = request.POST.get('priority', 1)
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')

        if not all([title, message, banner_type, color_scheme]):
            messages.error(request, 'Title, message, banner type, and color scheme are required.')
            return redirect('ordinances:admin_banner_edit', banner_id=banner_id)

        try:
            # Parse dates
            start_datetime = banner.start_date
            if start_date:
                start_datetime = timezone.datetime.strptime(start_date, '%Y-%m-%dT%H:%M')
                start_datetime = timezone.make_aware(start_datetime)

            end_datetime = None
            if end_date:
                end_datetime = timezone.datetime.strptime(end_date, '%Y-%m-%dT%H:%M')
                end_datetime = timezone.make_aware(end_datetime)

            # Update banner
            banner.title = title
            banner.message = message
            banner.banner_type = banner_type
            banner.color_scheme = color_scheme
            banner.is_active = is_active
            banner.is_dismissible = is_dismissible
            banner.show_icon = show_icon
            banner.priority = int(priority)
            banner.start_date = start_datetime
            banner.end_date = end_datetime
            banner.updated_by = request.user
            banner.save()

            messages.success(request, f'Banner "{title}" updated successfully!')
            return redirect('ordinances:admin_banner_list')

        except Exception as e:
            messages.error(request, f'Error updating banner: {str(e)}')

    context = {
        'banner': banner,
        'banner_types': Banner.BANNER_TYPES,
        'color_schemes': Banner.BANNER_COLORS,
    }
    return render(request, 'ordinances/admin/banner_form.html', context)


@login_required
def admin_banner_delete(request, banner_id):
    """Delete banner"""
    banner = get_object_or_404(Banner, id=banner_id)

    if request.method == 'POST':
        banner_title = banner.title
        banner.delete()
        messages.success(request, f'Banner "{banner_title}" deleted successfully!')
        return redirect('ordinances:admin_banner_list')

    context = {'banner': banner}
    return render(request, 'ordinances/admin/banner_delete.html', context)


@login_required
@require_http_methods(["POST"])
def admin_banner_toggle(request, banner_id):
    """Toggle banner active status via AJAX"""
    banner = get_object_or_404(Banner, id=banner_id)
    banner.is_active = not banner.is_active
    banner.updated_by = request.user
    banner.save()

    return JsonResponse({
        'success': True,
        'is_active': banner.is_active,
        'message': f'Banner {"activated" if banner.is_active else "deactivated"} successfully!'
    })


def admin_access_portal(request):
    """Secure admin access portal - no authentication required to view, but provides secure entry point"""
    # This view is intentionally public to provide a secure entry point
    # The actual admin functionality requires authentication
    return render(request, 'admin_access.html')


def admin_login(request):
    """Custom admin login view with redirect to admin dashboard"""
    from django.contrib.auth import authenticate, login
    from django.contrib.auth.forms import AuthenticationForm

    # If user is already authenticated and is staff, redirect to dashboard
    if request.user.is_authenticated and request.user.is_staff:
        return redirect('ordinances:admin_dashboard')

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)

            if user is not None and user.is_staff:
                login(request, user)
                messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')

                # Redirect to admin dashboard
                next_url = request.GET.get('next', 'ordinances:admin_dashboard')
                if next_url.startswith('/'):
                    return redirect(next_url)
                else:
                    return redirect(next_url)
            else:
                messages.error(request, 'Invalid credentials or insufficient permissions.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = AuthenticationForm()

    context = {
        'form': form,
    }
    return render(request, 'registration/login.html', context)


def admin_logout(request):
    """Custom admin logout view"""
    from django.contrib.auth import logout

    if request.user.is_authenticated:
        username = request.user.get_full_name() or request.user.username
        logout(request)
        messages.success(request, f'You have been successfully logged out. Goodbye, {username}!')

    return redirect('ordinances:home')
