from django.db import models
from django.utils.text import slugify
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.db.models import Q
from django.utils import timezone

User = get_user_model()


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, unique=True, blank=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Sponsor(models.Model):
    name = models.CharField(max_length=255)
    position = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.name} ({self.position})"


class Ordinance(models.Model):
    STATUS_CHOICES = [
        ("draft", "Draft"),
        ("reviewed", "Reviewed"),
        ("approved", "Approved"),
        ("published", "Published"),
    ]

    ordinance_number = models.CharField(max_length=100, unique=True)
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=300, unique=True, blank=True)
    content = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, related_name='ordinances')
    year_passed = models.PositiveIntegerField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="draft")
    sponsors = models.ManyToManyField(Sponsor, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_ordinances")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="updated_ordinances")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False)

    class Meta:
        ordering = ['-year_passed', 'ordinance_number']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['year_passed']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.ordinance_number} - {self.title}"

    def get_absolute_url(self):
        return reverse('ordinances:ordinance_detail', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(f"{self.ordinance_number}-{self.title}")
        super().save(*args, **kwargs)

    @classmethod
    def search(cls, query):
        """Search ordinances by title, content, or ordinance number"""
        if not query:
            return cls.objects.none()

        return cls.objects.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(ordinance_number__icontains=query)
        ).distinct()

    def is_public(self):
        """Check if ordinance is publicly viewable"""
        return self.status in ['approved', 'published']


class Attachment(models.Model):
    ordinance = models.ForeignKey(Ordinance, on_delete=models.CASCADE, related_name="attachments")
    file = models.FileField(upload_to="attachments/")
    description = models.CharField(max_length=255, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Attachment for {self.ordinance}"


class OrdinanceLog(models.Model):
    ordinance = models.ForeignKey(Ordinance, on_delete=models.CASCADE, related_name="logs")
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user} {self.action} on {self.ordinance}"


class Official(models.Model):
    POSITION_CHOICES = [
        ('mayor', 'Municipal Mayor'),
        ('vice_mayor', 'Vice Mayor'),
        ('councilor', 'Sangguniang Bayan Member'),
        ('secretary', 'Municipal Secretary'),
        ('treasurer', 'Municipal Treasurer'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('retired', 'Retired'),
    ]

    name = models.CharField(max_length=200)
    position = models.CharField(max_length=50, choices=POSITION_CHOICES)
    committee = models.CharField(max_length=200, blank=True, help_text="Committee or department headed")
    profile_picture = models.ImageField(upload_to='officials/', blank=True, null=True)
    bio = models.TextField(blank=True, help_text="Brief biography or description")
    achievements = models.TextField(blank=True, help_text="Key achievements (one per line)")
    term_start = models.DateField(help_text="Start of current term")
    term_end = models.DateField(help_text="End of current term")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'position', 'name']

    def __str__(self):
        return f"{self.name} - {self.get_position_display()}"

    def get_achievements_list(self):
        """Return achievements as a list"""
        if self.achievements:
            return [achievement.strip() for achievement in self.achievements.split('\n') if achievement.strip()]
        return []

    def get_profile_picture_url(self):
        """Return profile picture URL or default placeholder"""
        if self.profile_picture:
            return self.profile_picture.url
        return None

    @property
    def is_mayor(self):
        return self.position == 'mayor'

    @property
    def is_vice_mayor(self):
        return self.position == 'vice_mayor'


class Banner(models.Model):
    """Model for admin-controlled announcement banners"""

    BANNER_TYPES = [
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('success', 'Success'),
        ('error', 'Error'),
        ('announcement', 'Announcement'),
    ]

    BANNER_COLORS = [
        ('blue', 'Blue (Information)'),
        ('yellow', 'Yellow (Warning)'),
        ('green', 'Green (Success)'),
        ('red', 'Red (Error)'),
        ('purple', 'Purple (Announcement)'),
        ('gray', 'Gray (Neutral)'),
    ]

    title = models.CharField(max_length=200, help_text="Banner title or label")
    message = models.TextField(help_text="Banner message content")
    banner_type = models.CharField(max_length=20, choices=BANNER_TYPES, default='info')
    color_scheme = models.CharField(max_length=20, choices=BANNER_COLORS, default='blue')

    # Display settings
    is_active = models.BooleanField(default=True, help_text="Show this banner on the website")
    is_dismissible = models.BooleanField(default=True, help_text="Allow users to dismiss this banner")
    show_icon = models.BooleanField(default=True, help_text="Show icon in banner")

    # Scheduling
    start_date = models.DateTimeField(default=timezone.now, help_text="When to start showing this banner")
    end_date = models.DateTimeField(null=True, blank=True, help_text="When to stop showing this banner (optional)")

    # Priority and ordering
    priority = models.PositiveIntegerField(default=1, help_text="Higher numbers show first (1-10)")

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_banners")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="updated_banners")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', '-created_at']
        verbose_name = "Banner"
        verbose_name_plural = "Banners"

    def __str__(self):
        return f"{self.title} ({self.get_banner_type_display()})"

    def is_currently_active(self):
        """Check if banner should be displayed based on date range and active status"""
        if not self.is_active:
            return False

        now = timezone.now()
        if self.start_date and now < self.start_date:
            return False

        if self.end_date and now > self.end_date:
            return False

        return True

    def get_css_classes(self):
        """Get CSS classes for banner styling based on color scheme"""
        color_map = {
            'blue': 'bg-blue-100 text-blue-900 border-blue-200',
            'yellow': 'bg-yellow-400 text-yellow-900 border-yellow-500',
            'green': 'bg-green-100 text-green-900 border-green-200',
            'red': 'bg-red-100 text-red-900 border-red-200',
            'purple': 'bg-purple-100 text-purple-900 border-purple-200',
            'gray': 'bg-gray-100 text-gray-900 border-gray-200',
        }
        return color_map.get(self.color_scheme, color_map['blue'])

    def get_icon_class(self):
        """Get Font Awesome icon class based on banner type"""
        icon_map = {
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle',
            'success': 'fas fa-check-circle',
            'error': 'fas fa-times-circle',
            'announcement': 'fas fa-bullhorn',
        }
        return icon_map.get(self.banner_type, icon_map['info'])

    @classmethod
    def get_active_banners(cls):
        """Get all currently active banners ordered by priority"""
        now = timezone.now()
        return cls.objects.filter(
            is_active=True,
            start_date__lte=now
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gte=now)
        ).order_by('-priority', '-created_at')

    @property
    def is_councilor(self):
        return self.position == 'councilor'
